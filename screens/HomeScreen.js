// screens/HomeScreen.js - Fixed with improved shake handling
import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Animated,
  Easing,
  Alert,
  Dimensions,
  Image,
  SafeAreaView,
  StatusBar,
  Linking
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Swipeable } from 'react-native-gesture-handler';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Location from 'expo-location';

const { width, height } = Dimensions.get('window');

const HomeScreen = ({ navigation, user, shakeDetected, matches, onShake, onDeleteMatch, onBlockUser, serverAddress }) => {
  // Animation value for shake indicator
  const shakeAnim = React.useRef(new Animated.Value(0)).current;
  
  // Floating shapes animated values
  const floatingShape1 = {
    x: useRef(new Animated.Value(0)).current,
    y: useRef(new Animated.Value(0)).current,
    rotate: useRef(new Animated.Value(0)).current
  };

  const floatingShape2 = {
    x: useRef(new Animated.Value(0)).current,
    y: useRef(new Animated.Value(0)).current,
    rotate: useRef(new Animated.Value(0)).current
  };

  const floatingShape3 = {
    x: useRef(new Animated.Value(0)).current,
    y: useRef(new Animated.Value(0)).current,
    rotate: useRef(new Animated.Value(0)).current
  };
  
  // State to track timers for matches
  const [matchTimers, setMatchTimers] = useState({});
  
  // State to store profile data for matches
  const [matchProfiles, setMatchProfiles] = useState({});

  // Timer references to clear intervals
  const timerRefs = useRef({});
  
  // Manual shake counter for debug
  const [manualShakeCount, setManualShakeCount] = useState(0);
  const [lastManualShake, setLastManualShake] = useState(null);

  // Location permission state
  const [locationPermission, setLocationPermission] = useState(null);
  
  // Floating shapes animation setup
  useEffect(() => {
    // Create more complex animation sequences
    const createFloatingAnimation = (animValue, duration, xRange, yRange) => {
      Animated.loop(
        Animated.sequence([
          Animated.parallel([
            Animated.timing(animValue.x, {
              toValue: xRange[1],
              duration: duration,
              useNativeDriver: true,
              easing: Easing.inOut(Easing.ease)
            }),
            Animated.timing(animValue.y, {
              toValue: yRange[1],
              duration: duration,
              useNativeDriver: true,
              easing: Easing.inOut(Easing.ease)
            }),
            Animated.timing(animValue.rotate, {
              toValue: 1,
              duration: duration,
              useNativeDriver: true,
              easing: Easing.linear
            })
          ]),
          Animated.parallel([
            Animated.timing(animValue.x, {
              toValue: xRange[0],
              duration: duration,
              useNativeDriver: true,
              easing: Easing.inOut(Easing.ease)
            }),
            Animated.timing(animValue.y, {
              toValue: yRange[0],
              duration: duration,
              useNativeDriver: true,
              easing: Easing.inOut(Easing.ease)
            }),
            Animated.timing(animValue.rotate, {
              toValue: 0,
              duration: duration,
              useNativeDriver: true,
              easing: Easing.linear
            })
          ])
        ])
      ).start();
    };

    // Configure different animation parameters for each shape
    createFloatingAnimation(
      floatingShape1, 
      15000, 
      [-20, 20],   // X translation range
      [-30, 30]    // Y translation range
    );

    createFloatingAnimation(
      floatingShape2, 
      18000, 
      [-30, 30],   // Wider X translation
      [-40, 40]    // Wider Y translation
    );

    createFloatingAnimation(
      floatingShape3, 
      12000, 
      [-15, 15],   // Smaller X translation
      [-25, 25]    // Smaller Y translation
    );
  }, []); // Empty dependency array to run only once on mount
  
  // Update animation when shake is detected
  useEffect(() => {
    if (shakeDetected) {
      // Start pulse animation
      Animated.loop(
        Animated.sequence([
          Animated.timing(shakeAnim, {
            toValue: 1,
            duration: 500,
            useNativeDriver: true,
            easing: Easing.out(Easing.ease),
          }),
          Animated.timing(shakeAnim, {
            toValue: 0.5,
            duration: 500,
            useNativeDriver: true,
            easing: Easing.in(Easing.ease),
          })
        ]),
        { iterations: 4 }
      ).start();
    } else {
      // Reset animation
      shakeAnim.setValue(0);
    }
  }, [shakeDetected]);

  // Fetch profile data for all matches
  useEffect(() => {
    const fetchMatchProfiles = async () => {
      const profiles = { ...matchProfiles };
      let hasNewProfiles = false;

      for (const match of matches) {
        // Skip if we already have this profile
        if (profiles[match.userId] && profiles[match.userId].images) continue;
        
        try {
          // Check for cached profile first
          const cachedProfile = await AsyncStorage.getItem(`profile_${match.userId}`);
          
          if (cachedProfile) {
            const parsedProfile = JSON.parse(cachedProfile);
            profiles[match.userId] = parsedProfile;
            hasNewProfiles = true;
          } else if (serverAddress) {
            // Fetch from server if not cached
            try {
              const response = await fetch(`http://${serverAddress}/api/profile/${match.userId}`, {
                method: 'GET',
                headers: {
                  'Content-Type': 'application/json',
                },
              });
              
              if (response.ok) {
                const data = await response.json();
                profiles[match.userId] = data.profile;
                
                // Cache the profile
                await AsyncStorage.setItem(`profile_${match.userId}`, JSON.stringify(data.profile));
                hasNewProfiles = true;
              }
            } catch (error) {
              console.error(`Error fetching profile for ${match.userId}:`, error);
            }
          }
        } catch (error) {
          console.error('Error handling profile data:', error);
        }
      }
      
      if (hasNewProfiles) {
        setMatchProfiles(profiles);
      }
    };
    
    fetchMatchProfiles();
  }, [matches]);

  // Setup timers for matches
  useEffect(() => {
    // Clear any existing timers
    Object.values(timerRefs.current).forEach(clearInterval);
    timerRefs.current = {};
    const newTimers = {};

    matches.forEach(match => {
      if (match.createdAt && !match.hasActivity) {
        const createdTime = new Date(match.createdAt).getTime();
        const expiryTime = createdTime + 60000; // 1 minute expiry
        
        // Create a timer for this match
        timerRefs.current[match.userId] = setInterval(() => {
          const remainingTime = expiryTime - Date.now();
          
          if (remainingTime <= 0) {
            // Clear the timer if expired
            clearInterval(timerRefs.current[match.userId]);
            delete timerRefs.current[match.userId];
            newTimers[match.userId] = '0s';
          } else {
            // Calculate remaining seconds
            newTimers[match.userId] = `${Math.ceil(remainingTime / 1000)}s`;
          }
          
          setMatchTimers(prev => ({...prev, ...newTimers}));
        }, 1000);
      }
    });

    // Cleanup function
    return () => {
      Object.values(timerRefs.current).forEach(clearInterval);
    };
  }, [matches]);

  useEffect(() => {
    // Update header with profile button on left side and better spacing
    navigation.setOptions({
      headerShown: false, // Hide the default header for a full-screen design
    });
  }, [navigation]);

  // Check location permission on component mount
  useEffect(() => {
    checkLocationPermission();
  }, []);

  const checkLocationPermission = async () => {
    try {
      const { status } = await Location.getForegroundPermissionsAsync();
      setLocationPermission(status);
    } catch (error) {
      console.error('Error checking location permission:', error);
      setLocationPermission('denied');
    }
  };

  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      setLocationPermission(status);

      if (status !== 'granted') {
        Alert.alert(
          "Location Permission Required",
          "Shake & Match needs location permission to find matches near you. Please enable location in your device settings.",
          [
            { text: "Cancel", style: "cancel" },
            {
              text: "Open Settings",
              onPress: () => {
                if (Platform.OS === 'ios') {
                  Linking.openURL('app-settings:');
                } else {
                  Linking.openSettings();
                }
              }
            }
          ]
        );
      }
    } catch (error) {
      console.error('Error requesting location permission:', error);
      Alert.alert('Error', 'Failed to request location permission.');
    }
  };

  // Scale animation based on shake state
  const scale = shakeAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [1, 1.2],
  });

  // Handle navigation to chat screen safely
  const handleChatNavigation = (matchItem) => {
    if (!matchItem || !matchItem.userId) {
      // Display error if match data is invalid
      Alert.alert(
        "Error",
        "Cannot open chat with this match. Try restarting the app.",
        [{ text: "OK" }]
      );
      return;
    }
    
    // Clear unread messages flag
    matchItem.hasUnreadMessages = false;
    
    // Navigate with valid match data
    navigation.navigate('Chat', { match: matchItem });
  };

  // Handle profile view navigation
  const handleViewProfile = (matchItem) => {
    if (!matchItem || !matchItem.userId) {
      Alert.alert(
        "Error",
        "Cannot view profile. Try restarting the app.",
        [{ text: "OK" }]
      );
      return;
    }
    
    navigation.navigate('ViewProfile', { 
      userId: matchItem.userId,
      username: matchItem.username,
      forceRefresh: true // Always force refresh from home screen
    });
  };

  // Handle manual shake button press - for testing
  const handleManualShake = () => {
    console.log("Manual shake button pressed");

    // Check location permission first
    if (locationPermission !== 'granted') {
      Alert.alert(
        "Location Required",
        "You need to enable location permission to use the shake feature. This helps us find matches near you.",
        [
          { text: "Cancel", style: "cancel" },
          {
            text: "Enable Location",
            onPress: requestLocationPermission
          }
        ]
      );
      return;
    }

    const now = new Date();

    // Rate limit manual shakes to prevent spamming
    if (lastManualShake && (now - lastManualShake) < 2000) {
      console.log("Manual shake ignored - too soon");
      return;
    }

    setLastManualShake(now);
    setManualShakeCount(prev => prev + 1);

    // Use the same shake handler as accelerometer
    if (onShake) {
      onShake();
    }
  };

  // Render right actions for swipe
  const renderRightActions = (progress, matchId, matchUsername) => {
    const translateX = progress.interpolate({
      inputRange: [0, 1],
      outputRange: [160, 0],
    });

    return (
      <View style={styles.swipeActionsContainer}>
        <Animated.View style={[styles.actionContainer, { transform: [{ translateX }] }]}>
          <TouchableOpacity
            style={styles.deleteAction}
            onPress={() => {
              if (onDeleteMatch) {
                onDeleteMatch(matchId);
              }
            }}
          >
            <Ionicons name="trash-outline" size={24} color="white" />
            <Text style={styles.actionText}>Delete</Text>
          </TouchableOpacity>
        </Animated.View>
        
        <Animated.View style={[styles.actionContainer, { transform: [{ translateX }] }]}>
          <TouchableOpacity
            style={styles.unmatchAction}
            onPress={() => {
              Alert.alert(
                "Unmatch User",
                `Are you sure you want to unmatch with ${matchUsername}?`,
                [
                  { text: "Cancel", style: "cancel" },
                  {
                    text: "Unmatch",
                    style: "destructive",
                    onPress: () => {
                      if (onBlockUser) {
                        onBlockUser(matchId);
                      }
                    }
                  }
                ]
              );
            }}
          >
            <Ionicons name="person-remove-outline" size={24} color="white" />
            <Text style={styles.actionText}>Unmatch</Text>
          </TouchableOpacity>
        </Animated.View>
      </View>
    );
  };

  // Format distance properly
  const formatDistance = (distance) => {
    // Check if distance exists and is a number
    if (distance !== undefined && distance !== null && !isNaN(parseFloat(distance))) {
      return `${Math.round(parseFloat(distance) * 10) / 10} km away`;
    }
    return 'Distance unknown';
  };

  // Format match creation time
  const formatMatchTime = (createdAt) => {
    if (!createdAt) return '';
    
    try {
      const created = new Date(createdAt);
      const now = new Date();
      const diffMs = now - created;
      
      // If match is less than 1 minute old, show "Just now"
      if (diffMs < 60000) {
        return 'Just now';
      }
      
      // Show time ago in minutes if less than 60 minutes
      if (diffMs < 3600000) {
        const minutes = Math.floor(diffMs / 60000);
        return `${minutes} min ago`;
      }
      
      // Show time ago in hours if less than 24 hours
      if (diffMs < 86400000) {
        const hours = Math.floor(diffMs / 3600000);
        return `${hours} hr ago`;
      }
      
      // Otherwise show the date
      return created.toLocaleDateString();
    } catch (e) {
      return '';
    }
  };

  // Render match avatar - shows profile image or letter fallback
  const renderMatchAvatar = (item) => {
    const profile = matchProfiles[item.userId];
    const hasProfileImage = profile && profile.images && profile.images.length > 0;
    
    if (hasProfileImage) {
      const imageUri = profile.images[0].startsWith('data:') 
        ? profile.images[0] 
        : `data:image/jpeg;base64,${profile.images[0]}`;
      
      return (
        <View style={styles.avatarContainer}>
          <Image 
            source={{ uri: imageUri }} 
            style={styles.avatarImage} 
            resizeMode="cover"
          />

        </View>
      );
    }
    
    // Fallback to letter avatar if no image available
    return (
      <View style={styles.avatarContainer}>
        <Text style={styles.avatarText}>
          {item.username ? item.username.charAt(0).toUpperCase() : '?'}
        </Text>

      </View>
    );
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar barStyle="dark-content" backgroundColor="#f5f5f5" />
      
      <GestureHandlerRootView style={styles.container}>
        {/* Decorative floating shapes */}
        <Animated.View 
          style={[
            styles.floatingShape, 
            styles.floatingShape1,
            { 
              transform: [
                { translateX: floatingShape1.x },
                { translateY: floatingShape1.y },
                { rotate: floatingShape1.rotate.interpolate({
                  inputRange: [0, 1],
                  outputRange: ['0deg', '45deg']
                }) }
              ]
            }
          ]} 
        />
        <Animated.View 
          style={[
            styles.floatingShape, 
            styles.floatingShape2,
            { 
              transform: [

                { translateX: floatingShape2.x },
                { translateY: floatingShape2.y },
                { rotate: floatingShape2.rotate.interpolate({
                  inputRange: [0, 1],
                  outputRange: ['0deg', '-60deg']
                }) }
              ]
            }
          ]} 
        />
        <Animated.View 
          style={[
            styles.floatingShape, 
            styles.floatingShape3,
            { 
              transform: [
                { translateX: floatingShape3.x },
                { translateY: floatingShape3.y },
                { rotate: floatingShape3.rotate.interpolate({
                  inputRange: [0, 1],
                  outputRange: ['0deg', '90deg']
                }) }
              ]
            }
          ]} 
        />
        
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.headerButtonLeft}
            onPress={() => navigation.navigate('ProfileScreen')}
          >
            <Ionicons name="person-circle-outline" size={28} color="#555" />
          </TouchableOpacity>
          
          <Text style={styles.headerTitle}>Shake & Match</Text>
          
          <TouchableOpacity
            style={styles.headerButtonRight}
            onPress={() => navigation.navigate('Settings')}
          >
            <Ionicons name="settings-outline" size={28} color="#555" />
          </TouchableOpacity>
        </View>
        
        {/* Welcome section */}
        <View style={styles.profileSection}>
          <Text style={styles.welcomeText}>Hi, {user.username}!</Text>
          <Text style={styles.instructionText}>
            Shake your phone to match with others! ♡
          </Text>
        </View>
        
        {/* Shake section with enhanced design */}
        <View style={styles.shakeSection}>
          <Animated.View
            style={[
              styles.shakeOuterCircle,
              { transform: [{ scale }] }
            ]}
          >
            <TouchableOpacity
              style={[
                styles.shakeButton,
                locationPermission !== 'granted' && styles.shakeButtonDisabled
              ]}
              onPress={handleManualShake}
              activeOpacity={0.7}
            >
              <Ionicons
                name={locationPermission === 'granted' ? "phone-portrait" : "location-outline"}
                size={50}
                color="#fff"
              />
              <Text style={styles.shakeText}>
                {locationPermission === 'granted' ? 'SHAKE' : 'ENABLE LOCATION'}
              </Text>
            </TouchableOpacity>
          </Animated.View>

          {/* Fixed height container for the bubble to prevent layout shifts */}
          <View style={styles.bubbleContainer}>
            {shakeDetected && (
              <View style={styles.searchingBubble}>
                <Text style={styles.searchingText}>
                  Searching for nearby matches...
                </Text>
              </View>
            )}
          </View>
        </View>
        
        {/* Matches card container */}
        <View style={styles.matchesContainer}>
          <Text style={styles.sectionTitle}>Your Matches</Text>
          
          {matches.length === 0 ? (
            <View style={styles.emptyMatchesCard}>
              <Ionicons name="people-outline" size={48} color="#ccc" />
              <Text style={styles.noMatchesText}>
                No matches yet.
              </Text>
            </View>
          ) : (
            <FlatList
              data={matches}
              keyExtractor={(item) => item.userId || Math.random().toString()}
              renderItem={({ item }) => (
                <Swipeable
                  renderRightActions={(progress) => 
                    renderRightActions(progress, item.userId, item.username)
                  }
                  overshootRight={false}
                >
                  <View style={styles.matchItem}>
                    <TouchableOpacity
                      onPress={() => handleViewProfile(item)}
                    >
                      {renderMatchAvatar(item)}
                    </TouchableOpacity>
                    
                    <TouchableOpacity 
                      style={styles.matchInfo}
                      onPress={() => handleViewProfile(item)}
                    >
                      <Text style={styles.matchName}>{item.username || 'Unknown User'}</Text>
                      <View style={styles.matchDetailsRow}>
                        <Text style={styles.matchDistance}>
                          {formatDistance(item.distance)}
                        </Text>
                        {item.createdAt && !item.hasActivity && (
                          <View style={styles.matchTimestampContainer}>
                            <Text style={styles.matchTimestamp}>
                              {formatMatchTime(item.createdAt)}
                            </Text>
                            {matchTimers[item.userId] && (
                              <Text style={styles.expiryWarning}>
                                • {matchTimers[item.userId]}
                              </Text>
                            )}
                          </View>
                        )}
                      </View>
                    </TouchableOpacity>
                    
                    {item.hasUnreadMessages && (
                      <View style={styles.unreadBadge}>
                        <Text style={styles.unreadBadgeText}>New</Text>
                      </View>
                    )}
                    
                    <TouchableOpacity 
                      style={styles.chatButton}
                      onPress={() => handleChatNavigation(item)}
                    >
                      <Ionicons name="chatbubble-outline" size={24} color="#4e9af1" />
                    </TouchableOpacity>
                  </View>
                </Swipeable>
              )}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.matchesListContent}
            />
          )}
        </View>
      </GestureHandlerRootView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  container: {
    flex: 1,
    position: 'relative',
  },
  // Floating shapes styling
  floatingShape: {
    position: 'absolute',
    borderRadius: 80,
    zIndex: -1,
  },
  floatingShape1: {
    width: 180,
    height: 180,
    top: -40,
    right: -40,
    backgroundColor: 'rgba(245, 245, 245, 0.7)',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  floatingShape2: {
    width: 220,
    height: 220,
    top: '50%',
    left: -110,
    backgroundColor: 'rgba(245, 245, 245, 0.5)',
    borderWidth: 1,
    borderColor: '#e8e8e8',
  },
  floatingShape3: {
    width: 150,
    height: 150,
    bottom: 100,
    right: -30,
    backgroundColor: 'rgba(245, 245, 245, 0.6)',
    borderWidth: 1,
    borderColor: '#e8e8e8',
  },
  // Header styling
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eaeaea',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  headerButtonLeft: {
    padding: 8,
  },
  headerButtonRight: {
    padding: 8,
  },
  // Profile section styling
  profileSection: {
    padding: 25,
    alignItems: 'center',
  },
  welcomeText: {
    fontSize: 26,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333',
  },
  instructionText: {
    fontSize: 16,
    textAlign: 'center',
    color: '#666',
    lineHeight: 22,
  },
  // Shake section styling
  shakeSection: {
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 20,
  },
  shakeOuterCircle: {
    width: 180,
    height: 180,
    borderRadius: 90,
    backgroundColor: '#f8f8f8',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1.5,
    borderColor: '#e0e0e0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  shakeButton: {
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: '#4e9af1',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#4e9af1',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 5,
  },
  shakeButtonDisabled: {
    backgroundColor: '#ff9500',
    shadowColor: '#ff9500',
  },
  shakeText: {
    color: '#fff',
    marginTop: 8,
    fontWeight: 'bold',
    fontSize: 16,
    letterSpacing: 1.5,
  },
  bubbleContainer: {
    height: 50, // Fixed height to prevent layout shifts
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
  },
  searchingBubble: {
    backgroundColor: 'rgba(78, 154, 241, 0.1)',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 30,
    borderWidth: 1,
    borderColor: 'rgba(78, 154, 241, 0.3)',
  },
  searchingText: {
    fontSize: 16,
    color: '#4e9af1',
    fontWeight: '500',
  },
  // Matches section styling
  matchesContainer: {
    flex: 1,
    backgroundColor: 'transparent',
    marginHorizontal: 20,
    marginBottom: 20,
    padding: 0,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
    marginTop: 5,
    marginLeft: 5,
  },
  matchesListContent: {
    paddingBottom: 10,
  },
  emptyMatchesCard: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
    borderRadius: 15,
    backgroundColor: '#f8fafc',
    marginTop: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  noMatchesText: {
    textAlign: 'center',
    color: '#999',
    marginTop: 15,
    fontSize: 16,
    lineHeight: 22,
  },
  matchItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    backgroundColor: '#f8fafc',
    borderRadius: 15,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  avatarContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#4e9af1',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
    position: 'relative',
    overflow: 'hidden',
    shadowColor: '#4e9af1',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  avatarImage: {
    width: '100%',
    height: '100%',
    borderRadius: 25,
  },
  avatarText: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
  },

  matchInfo: {
    flex: 1,
  },
  matchName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2c384a',
  },
  matchDetailsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  matchTimestampContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 8,
  },
  matchTimestamp: {
    fontSize: 12,
    color: '#8a9cb0',
  },
  expiryWarning: {
    fontSize: 12,
    color: '#ff3b30',
    fontWeight: '500',
    marginLeft: 4,
  },
  matchDistance: {
    fontSize: 14,
    color: '#8a9cb0',
  },
  unreadBadge: {
    backgroundColor: '#ff3b30',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 10,
    marginRight: 10,
  },
  unreadBadgeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  chatButton: {
    width: 45,
    height: 45,
    borderRadius: 22.5,
    backgroundColor: '#f0f8ff',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e8eef4',
  },
  swipeActionsContainer: {
    flexDirection: 'row',
    width: 170,
    height: 'auto',
  },
  actionContainer: {
    height: 'auto',
  },
  deleteAction: {
    backgroundColor: '#ff5a52',
    justifyContent: 'center',
    alignItems: 'center',
    width: 85,
    padding: 15,
    height: '90%',
    borderTopRightRadius: 0,
    borderBottomRightRadius: 0,
    borderTopLeftRadius: 12,
    borderBottomLeftRadius: 12,
    shadowColor: '#ff5a52',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 3,
  },
  unmatchAction: {
    backgroundColor: '#5b7bf0',
    justifyContent: 'center',
    alignItems: 'center',
    width: 85,
    padding: 15,
    height: '90%',
    borderTopLeftRadius: 0,
    borderBottomLeftRadius: 0,
    borderTopRightRadius: 12,
    borderBottomRightRadius: 12,
    shadowColor: '#5b7bf0',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 3,
  },
  actionText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 12,
    marginTop: 4,
  },
});
  
export default HomeScreen;