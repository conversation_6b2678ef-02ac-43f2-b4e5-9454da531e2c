// screens/LoginScreen.js - Modern design with fixed keyboard issues
import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  SafeAreaView,
  StatusBar,
  Image,
  Platform,
  Keyboard,
  Dimensions,
  Animated
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';

const LoginScreen = ({ navigation, onLogin, serverAddress: propServerAddress }) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [serverAddress, setServerAddress] = useState(propServerAddress || '**************:3000');
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const [screenHeight, setScreenHeight] = useState(Dimensions.get('window').height);
  
  // Animation values
  const logoPosition = useRef(new Animated.Value(0)).current;
  const formOpacity = useRef(new Animated.Value(1)).current;
  
  // Reference for password input
  const passwordInputRef = useRef(null);

  // Handle keyboard appearance and animations
  useEffect(() => {
    const keyboardWillShowListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
      (event) => {
        setKeyboardVisible(true);
        // Calculate how much we need to move the form up to avoid keyboard
        const keyboardHeight = event.endCoordinates.height;
        // Calculate screen space and adjust movement accordingly
        const availableSpace = screenHeight - keyboardHeight;
        // Move the entire container up enough to ensure form is visible
        // On iOS we need to consider the keyboard takes up more space
        const formHeight = 300; // Estimated form height
        const contentHeight = 450; // Estimated total content height
        const availableHeight = screenHeight - keyboardHeight;
        
        // Calculate how much we need to move to ensure fields are visible
        const moveUpValue = Platform.OS === 'ios' 
          ? -Math.min(contentHeight - availableHeight + 200, 250) 
          : -Math.min(keyboardHeight * 0.5, 150);
        
        Animated.parallel([
          Animated.timing(logoPosition, {
            toValue: moveUpValue,
            duration: 300,
            useNativeDriver: true
          }),
          Animated.timing(formOpacity, {
            toValue: 0.97,
            duration: 300,
            useNativeDriver: true
          })
        ]).start();
      }
    );
    
    const keyboardWillHideListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
      () => {
        setKeyboardVisible(false);
        Animated.parallel([
          Animated.timing(logoPosition, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true
          }),
          Animated.timing(formOpacity, {
            toValue: 1,
            duration: 300,
            useNativeDriver: true
          })
        ]).start();
      }
    );

    // Screen dimensions listener for orientation changes
    const dimensionListener = Dimensions.addEventListener(
      'change',
      ({ window }) => {
        setScreenHeight(window.height);
      }
    );

    // Cleanup
    return () => {
      keyboardWillShowListener.remove();
      keyboardWillHideListener.remove();
      dimensionListener.remove();
    };
  }, []);

  const handleLogin = async () => {
    Keyboard.dismiss();
    
    // Validation
    if (!username.trim() || !password.trim()) {
      Alert.alert('Error', 'Please enter username and password');
      return;
    }

    setIsLoading(true);

    try {
      await AsyncStorage.setItem('serverAddress', serverAddress);
      console.log(`Attempting login for ${username} to http://${serverAddress}/api/login`);
      
      // Log the exact request being sent
      const requestBody = JSON.stringify({
        username,
        password,
      });
      console.log('Request payload:', requestBody);
      
      try {
        // First attempt with more detailed error handling
        const response = await fetch(`http://${serverAddress}/api/login`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          body: requestBody,
        });

        console.log('Response status:', response.status);
        console.log('Response headers:', JSON.stringify(response.headers));
        
        // Get the raw text first
        const responseText = await response.text();
        console.log('Raw response text:', responseText);
        
        // Check if response is empty
        if (!responseText) {
          console.error('Empty response received from server');
          throw new Error('Server returned an empty response');
        }
        
        // Try to parse as JSON
        let data;
        try {
          data = JSON.parse(responseText);
          console.log('Parsed response data:', JSON.stringify(data));
        } catch (parseError) {
          console.error('Error parsing response as JSON:', parseError);
          throw new Error('Server returned invalid JSON: ' + responseText.substring(0, 100));
        }

        if (!response.ok) {
          throw new Error(data.message || 'Login failed');
        }

        // Extract userId with detailed logging
        console.log('Looking for userId in response...');
        const userId = data.userId || (data.user && data.user.id);
        console.log('Extracted userId:', userId);
        
        if (!userId) {
          console.error('User ID missing from response:', JSON.stringify(data));
          throw new Error('User ID missing from server response');
        }

        // Create user data object
        const userData = {
          id: userId,
          username: username,
        };

        console.log('Login successful, user data:', JSON.stringify(userData));
        
        // Test storing in AsyncStorage before calling onLogin
        try {
          await AsyncStorage.setItem('test_user', JSON.stringify(userData));
          const testRead = await AsyncStorage.getItem('test_user');
          console.log('Test AsyncStorage read:', testRead);
        } catch (storageError) {
          console.error('AsyncStorage test failed:', storageError);
        }
        
        // Call the onLogin function
        console.log('Calling onLogin with userData');
        onLogin(userData, serverAddress);
      } catch (fetchError) {
        console.error('Fetch operation error:', fetchError);
        // Try an alternative approach with XMLHttpRequest for diagnostic purposes
        console.log('Attempting fallback XMLHttpRequest...');
        
        const xhr = new XMLHttpRequest();
        xhr.onreadystatechange = function() {
          if (xhr.readyState === 4) {
            console.log('XHR Status:', xhr.status);
            console.log('XHR Response:', xhr.responseText);
            
            if (xhr.status >= 200 && xhr.status < 300) {
              try {
                const xhrData = JSON.parse(xhr.responseText);
                console.log('XHR Parsed data:', xhrData);
                
                const xhrUserId = xhrData.userId || (xhrData.user && xhrData.user.id);
                if (xhrUserId) {
                  const xhrUserData = {
                    id: xhrUserId,
                    username: username,
                  };
                  console.log('XHR Login successful:', xhrUserData);
                  onLogin(xhrUserData, serverAddress);
                } else {
                  throw new Error('User ID missing from XHR response');
                }
              } catch (e) {
                console.error('XHR parse error:', e);
                throw new Error('Failed to process XHR response');
              }
            } else {
              throw new Error(`XHR failed with status ${xhr.status}`);
            }
          }
        };
        
        xhr.open('POST', `http://${serverAddress}/api/login`, true);
        xhr.setRequestHeader('Content-Type', 'application/json');
        xhr.setRequestHeader('Accept', 'application/json');
        xhr.send(requestBody);
        
        throw fetchError; // Still throw the original error
      }
    } catch (error) {
      console.error('Login error:', error);
      Alert.alert(
        'Login Failed',
        `${error.message || 'Invalid username or password. Please try again.'}`
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar barStyle="light-content" backgroundColor="#4e9af1" />
      
      <Animated.View 
        style={[
          styles.container,
          { transform: [{ translateY: logoPosition }] }
        ]}
      >
        {/* Background gradient */}
        <View style={styles.backgroundGradient} />
        
        {/* Floating shapes - purely decorative */}
        <View style={[styles.floatingShape, styles.floatingShape1]} />
        <View style={[styles.floatingShape, styles.floatingShape2]} />
        <View style={[styles.floatingShape, styles.floatingShape3]} />
        
        {/* Logo section */}
        <View style={styles.logoContainer}>
          <Image
            source={require('../assets/logo.png')}
            style={styles.logo}
            resizeMode="contain"
            fadeDuration={0}
          />
          <Text style={styles.appName}>Shake & Match</Text>
          <Text style={styles.tagline}>Sign in to your account</Text>
        </View>

        {/* Form section with animation */}
        <Animated.View 
          style={[
            styles.formContainer,
            { 
              opacity: formOpacity,
              transform: [
                { 
                  scale: formOpacity.interpolate({
                    inputRange: [0.97, 1],
                    outputRange: [0.98, 1]
                  }) 
                }
              ] 
            }
          ]}
        >
          {/* Username input */}
          <View style={styles.fieldWrapper}>
            <View style={styles.inputContainer}>
              <Ionicons name="person-outline" size={24} color="#4e9af1" style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="Username"
                placeholderTextColor="#8a9cb0"
                value={username}
                onChangeText={setUsername}
                autoCapitalize="none"
                autoCorrect={false}
                spellCheck={false}
                returnKeyType="next"
                onSubmitEditing={() => passwordInputRef.current?.focus()}
                textContentType="username"
                autoComplete="username"
              />
            </View>
          </View>
          
          {/* Password input */}
          <View style={styles.fieldWrapper}>
            <View style={styles.inputContainer}>
              <Ionicons name="lock-closed-outline" size={24} color="#4e9af1" style={styles.inputIcon} />
              <TextInput
                ref={passwordInputRef}
                style={[styles.input, styles.passwordInput]}
                placeholder="Password"
                placeholderTextColor="#8a9cb0"
                value={password}
                onChangeText={setPassword}
                secureTextEntry={true}
                autoCapitalize="none"
                autoCorrect={false}
                spellCheck={false}
                returnKeyType="done"
                onSubmitEditing={handleLogin}
                textContentType="password"
                autoComplete="password"
                importantForAutofill="yes"
                passwordRules="minlength: 6;"
              />
            </View>
          </View>
          
          <TouchableOpacity
            style={styles.loginButton}
            onPress={handleLogin}
            disabled={isLoading}
            activeOpacity={0.85}
          >
            {isLoading ? (
              <ActivityIndicator color="#fff" />
            ) : (
              <>
                <Text style={styles.loginButtonText}>Sign In</Text>
                <Ionicons name="arrow-forward" size={20} color="#fff" style={styles.buttonIcon} />
              </>
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.registerButton}
            onPress={() => {
              Keyboard.dismiss();
              navigation.navigate('Register');
            }}
            activeOpacity={0.7}
          >
            <Text style={styles.registerButtonText}>
              Don't have an account? <Text style={styles.registerButtonTextBold}>Create one</Text>
            </Text>
          </TouchableOpacity>
        </Animated.View>
      </Animated.View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#4e9af1',
  },
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 25,
    paddingTop: Platform.OS === 'ios' ? 20 : 0,
    paddingBottom: Platform.OS === 'ios' ? 40 : 20,
  },
  backgroundGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#4e9af1',
    zIndex: -2,
  },
  floatingShape: {
    position: 'absolute',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 80,
    zIndex: -1,
  },
  floatingShape1: {
    width: 200,
    height: 200,
    top: -60,
    right: -60,
    transform: [{ rotate: '30deg' }],
  },
  floatingShape2: {
    width: 180,
    height: 180,
    top: '65%',
    left: -90,
    transform: [{ rotate: '15deg' }],
  },
  floatingShape3: {
    width: 120,
    height: 120,
    bottom: 100,
    right: -20,
    transform: [{ rotate: '45deg' }],
    opacity: 0.6,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 35,
    width: '100%',
  },
  logo: {
    width: 110,
    height: 110,
  },
  appName: {
    fontSize: 26,
    fontWeight: 'bold',
    marginTop: 12,
    color: '#fff',
  },
  tagline: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 5,
  },
  formContainer: {
    width: '100%',
    backgroundColor: 'white',
    padding: 25,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.15,
    shadowRadius: 20,
    elevation: 8,
  },
  fieldWrapper: {
    marginVertical: 10,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1.5,
    borderColor: '#e8eef4',
    borderRadius: 14,
    paddingHorizontal: 18,
    height: 60,
    backgroundColor: '#f8fafc',
  },
  inputIcon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: '#2c384a',
    fontWeight: '500',
    paddingVertical: Platform.OS === 'ios' ? 18 : 16,
    textAlignVertical: 'center',
  },
  passwordInput: {
    paddingVertical: Platform.OS === 'ios' ? 18 : 16,
    textAlignVertical: 'center',
    ...(Platform.OS === 'android' && {
      includeFontPadding: false,
      textAlignVertical: 'center',
    }),
  },
  loginButton: {
    backgroundColor: '#4e9af1',
    paddingVertical: 16,
    borderRadius: 14,
    alignItems: 'center',
    marginTop: 25,
    flexDirection: 'row',
    justifyContent: 'center',
    shadowColor: '#4e9af1',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.35,
    shadowRadius: 12,
    elevation: 6,
  },
  loginButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
  },
  buttonIcon: {
    marginLeft: 8,
  },
  registerButton: {
    alignItems: 'center',
    marginTop: 20,
    padding: 12,
  },
  registerButtonText: {
    color: '#8a9cb0',
    fontSize: 15,
  },
  registerButtonTextBold: {
    color: '#4e9af1',
    fontWeight: '600',
  }
});

export default LoginScreen;